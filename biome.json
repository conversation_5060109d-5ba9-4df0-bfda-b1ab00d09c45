{"$schema": "./node_modules/@biomejs/biome/configuration_schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "javascript": {"parser": {"unsafeParameterDecoratorsEnabled": true}, "formatter": {"quoteStyle": "single", "jsxQuoteStyle": "double"}}, "formatter": {"indentStyle": "space", "indentWidth": 2, "lineWidth": 100}, "linter": {"rules": {"recommended": true, "a11y": "off", "correctness": {"noUnusedImports": "warn", "noUnusedVariables": "off"}, "style": {"useImportType": "off", "noNonNullAssertion": "off"}, "suspicious": {"noArrayIndexKey": "off", "noAssignInExpressions": "warn", "noImplicitAnyLet": "warn"}, "security": {"noDangerouslySetInnerHtml": "warn", "noBlankTarget": "warn"}}}, "overrides": [{"includes": ["apps/youapi/**/*"], "linter": {"rules": {"style": {"useImportType": "off"}, "correctness": {"useHookAtTopLevel": "off", "noUnusedImports": {"level": "error", "fix": "safe"}, "noUnreachable": "warn", "useYield": "warn", "noUnknownProperty": "warn"}, "suspicious": {"noImplicitAnyLet": "off", "noAssignInExpressions": "warn", "noArrayIndexKey": "warn", "noDoubleEquals": "warn", "noDuplicateProperties": "warn", "noControlCharactersInRegex": "warn"}}}}, {"includes": ["packages/ui/**/*"], "linter": {"rules": {"correctness": {"useExhaustiveDependencies": "error"}, "style": {"useImportType": "off"}}}}, {"includes": ["packages/di/**/*"], "linter": {"rules": {"complexity": {"noStaticOnlyClass": "off"}}}}, {"includes": ["apps/youhome/**/*"], "linter": {"rules": {"suspicious": {"noImplicitAnyLet": "warn", "noArrayIndexKey": "off"}, "security": {"noDangerouslySetInnerHtml": "off"}}}}, {"includes": ["packages/api/**/*"], "linter": {"rules": {"suspicious": {"noExplicitAny": "off", "noImplicitAnyLet": "off"}, "correctness": {"noUnusedImports": "off"}}}}, {"includes": ["apps/youweb/**/*"], "linter": {"rules": {"suspicious": {"noShadowRestrictedNames": "warn", "noDuplicateProperties": "warn", "noControlCharactersInRegex": "warn"}, "correctness": {"noUnusedImports": {"fix": "none", "level": "error"}, "noUnreachable": "warn"}}}}, {"includes": ["packages/ui-business-editor/**/*", "packages/editor-common/**/*"], "linter": {"rules": {"correctness": {"noUnusedImports": {"fix": "none", "level": "error"}}}}}]}