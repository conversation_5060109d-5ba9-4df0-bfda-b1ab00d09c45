import {
  AILanguageEnum,
  type AILanguageEnumKeys,
  type AllLanguageEnumKeys,
  iso6391ToLanguage,
  LanguageNameMap,
} from '@repo/common/types';
import { Button } from '@repo/ui/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@repo/ui/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@repo/ui/components/ui/popover';
import { useTrackActions } from '@repo/ui/lib/posthog/useTrackActions';
import { useYouMindTranslate } from '@youmindinc/youmind-translate';
import { Check, ChevronDown } from 'lucide-react';
import React from 'react';
import { cn } from '@/utils/utils';

interface LanguageSelectorProps {
  type: 'source' | 'target';
  value: AILanguageEnumKeys;
  onChange: (value: AILanguageEnumKeys) => void;
  align?: 'start' | 'center' | 'end';
  ghost?: boolean;
  disabled?: boolean;
  children?: React.ReactNode;
  hoverToOpen?: boolean;
}

/**
 * 翻译功能语言选择器组件
 */
export const TranslateLanguageSelector: React.FC<LanguageSelectorProps> = ({
  type,
  value,
  onChange,
  disabled = false,
  children,
  hoverToOpen = false,
}) => {
  const [open, setOpen] = React.useState(false);

  const { trackButtonClick } = useTrackActions();

  const { detectedLanguage } = useYouMindTranslate();

  // 延迟关闭的 timer ref
  const closeTimerRef = React.useRef<NodeJS.Timeout>();
  // 记录是否是通过悬停打开的，以及打开时间
  const hoverOpenTimeRef = React.useRef<number>(0);

  // 处理鼠标悬停事件
  const handleMouseEnter = () => {
    if (hoverToOpen && !disabled) {
      // 清除任何待关闭的 timer
      if (closeTimerRef.current) {
        clearTimeout(closeTimerRef.current);
        closeTimerRef.current = undefined;
      }

      // 如果还没有打开，则延迟打开
      if (!open) {
        setTimeout(() => {
          setOpen(true);
          hoverOpenTimeRef.current = Date.now(); // 记录悬停打开的时间
        }, 150);
      }
    }
  };

  const handleMouseLeave = () => {
    if (hoverToOpen && !disabled) {
      // 设置延迟关闭，给用户时间移动到浮窗
      closeTimerRef.current = setTimeout(() => {
        setOpen(false);
        closeTimerRef.current = undefined;
      }, 150); // 150ms 延迟
    }
  };

  // 处理点击事件
  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();

    // 如果启用了悬停打开功能
    if (hoverToOpen && !disabled) {
      const now = Date.now();
      const timeSinceHoverOpen = now - hoverOpenTimeRef.current;

      // 如果是悬停打开后的 500ms 内，忽略点击
      if (hoverOpenTimeRef.current > 0 && timeSinceHoverOpen < 500) {
        return;
      }
    }

    // 正常的点击切换逻辑
    setOpen(!open);
    // 如果是点击操作，重置悬停打开时间
    if (!hoverToOpen) {
      hoverOpenTimeRef.current = 0;
    }
  };

  // 清理定时器
  React.useEffect(() => {
    return () => {
      if (closeTimerRef.current) {
        clearTimeout(closeTimerRef.current);
      }
    };
  }, []);

  const isSourceLanguage = type === 'source';

  const browserUILan = iso6391ToLanguage[navigator.language] || 'en-US';
  // const browserUILan = "zh-CN";

  const handleLanguageChange = async (selectedValue: string) => {
    const newLanguage = selectedValue.split('__')[0] as AILanguageEnumKeys;
    onChange(newLanguage);
    // 上报埋点
    trackButtonClick('translate_language_change_click', {
      change_type: type,
      change_language: newLanguage,
    });
  };

  const renderLanguageName = (lan: AllLanguageEnumKeys) => {
    if (lan === AILanguageEnum['follow-content']) {
      // 容易检测的不对，先隐藏
      if (detectedLanguage) {
        // return `${LanguageNameMap[detectedLanguage as AllLanguageEnumKeys]} (Auto Detect)`;
        return 'Auto Detect';
      }
      return 'Auto Detect';
    }
    if (lan === browserUILan && !isSourceLanguage) {
      return `${LanguageNameMap[lan]} (System)`;
    }
    if (LanguageNameMap[lan]) {
      return LanguageNameMap[lan];
    }
    return lan;
  };

  // 获取所有可用语言
  const filteredLanguages = Object.values(AILanguageEnum).filter((lang) => {
    if (lang === AILanguageEnum['follow-content']) {
      return isSourceLanguage;
    }
    return lang !== 'system';
  });

  // 使用排序函数对语言进行排序
  const availableLanguages = [...filteredLanguages].sort((a, b) => {
    // 定义优先级函数
    const getPriority = (lang: string): number => {
      // 源语言选择器：自动检测 > 中文 > 其他
      if (isSourceLanguage) {
        if (lang === AILanguageEnum['follow-content']) return 1;
        if (lang === 'en-US') return 2;
        if (lang === 'zh-CN') return 3;
        return 4;
      }
      // 目标语言选择器：系统语言 > 中文 > 其他
      else {
        if (lang === browserUILan) return 1;
        if (lang === 'en-US') return 2;
        if (lang === 'zh-CN') return 3;
        return 4;
      }
    };

    // 比较优先级
    return getPriority(a) - getPriority(b);
  });

  // 处理 Popover 状态变化
  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen);
    // 如果关闭了，重置悬停打开时间
    if (!newOpen) {
      hoverOpenTimeRef.current = 0;
    }
  };

  return (
    <Popover open={open} onOpenChange={handleOpenChange}>
      <PopoverTrigger
        asChild
        disabled={disabled}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={handleClick}
      >
        {children ? (
          children
        ) : (
          <Button
            variant="ghost"
            className="flex h-[50px] w-[128px] flex-col items-start bg-card-snips px-2 py-[6px] hover:bg-background rounded-md"
            disabled={disabled}
          >
            <div className="flex w-full items-center justify-between gap-1">
              <div className="truncate text-sm font-normal text-foreground">
                {renderLanguageName(value)}
              </div>
              <ChevronDown className="h-4 w-4 shrink-0 text-caption-foreground" />
            </div>
            <div className="text-xs font-normal text-caption-foreground">
              {type === 'source' ? 'Source' : 'Target'}
            </div>
          </Button>
        )}
      </PopoverTrigger>
      <PopoverContent
        onOpenAutoFocus={(e) => e.preventDefault()}
        className="w-[200px] rounded-[12px] border-none !p-0 shadow-lg"
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        <Command>
          <CommandInput placeholder="Search language..." />
          <CommandList>
            <CommandEmpty>No language found.</CommandEmpty>
            <CommandGroup>
              {availableLanguages.map((lang, index) => (
                <CommandItem
                  key={`${index}__${lang}`}
                  value={`${lang}__${renderLanguageName(lang as AllLanguageEnumKeys)}`}
                  onSelect={(currentValue: string) => {
                    handleLanguageChange(currentValue);
                    setOpen(false);
                  }}
                >
                  <Check
                    className={cn('mr-2 h-4 w-4', value === lang ? 'opacity-100' : 'opacity-0')}
                  />
                  {renderLanguageName(lang as AllLanguageEnumKeys)}
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
};
