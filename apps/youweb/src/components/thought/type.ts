import type { EditCommand } from '@repo/common/types/chat/enum';
import type { ThoughtEditorReadyParams } from '@repo/ui-business-editor';
import { PatchThought, Thought, ThoughtTitleTypeEnum } from '@/typings/thought';
import { User } from '@/typings/user';
import type { SelectionType } from './const';
import type { IThoughtWorkflow } from './workflow';

export interface AIOptions {
  hasPendingCommand?: () => boolean;
  setAddCommandToListAndOpenChatPanel?: (command: EditCommand) => void;
  setAddMessageToListAndOpenChatPanel?: (message: string) => void;
}

export type ThoughtBodyComponentProps = {
  id: string;
  workflow?: IThoughtWorkflow;
  thought: Thought;
  onUpdate?: (data: PatchThought) => void;
  onSelectionChange?: (selection: OnSelectionChangeParams) => void;
  onReady?: (params: ThoughtEditorReadyParams) => void;
  aiOptions?: AIOptions;
  user: User;
};

// 外部不用传入 workflow
export type ThoughtBodyProps = Omit<ThoughtBodyComponentProps, 'workflow'>;

export interface OnSelectionChangeParams {
  plainText: string;
  selectionType: SelectionType;
}

export type ThoughtBodyComponentRef = {
  onUpdate: (data: PatchThought) => void;
  getTitle: () => string;
  setTitle: (title: string) => void;
  setIsGenTitle: (isGenTitle: boolean) => void;
  setTitleType: (titleType: ThoughtTitleTypeEnum) => void;
  getIsGenTitle: () => boolean;
  getTitleType: () => ThoughtTitleTypeEnum;
  onSelectionChange?: (selection: OnSelectionChangeParams) => void;
};
