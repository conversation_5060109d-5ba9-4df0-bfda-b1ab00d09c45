'use client';

import { User } from '@/typings/user';
import type { TranscriptContent } from '../atoms';

// Constants
export const speakerAvatars = [
  'https://cdn.gooo.ai/assets/Property=Wink.png',
  'https://cdn.gooo.ai/assets/Property=Spark.png',
  'https://cdn.gooo.ai/assets/Property=Calm.png',
  'https://cdn.gooo.ai/assets/Property=Cheer.png',
  'https://cdn.gooo.ai/assets/Property=Chill.png',
  'https://cdn.gooo.ai/assets/Property=Cat.png',
  'https://cdn.gooo.ai/assets/Property=Cool.png',
  'https://cdn.gooo.ai/assets/Property=Thinking.png',
];

// Utility functions
export function exportTranscript(content: string) {
  const fileName = 'transcript.txt';
  const link: HTMLAnchorElement = document.createElement('a');

  if (window.Blob && window.URL) {
    const blobObj = new Blob([content]);

    // account for IE
    // @see https://stackoverflow.com/a/********
    // @ts-expect-error - msSaveBlob is not in the types
    if (navigator.msSaveBlob) {
      // @ts-expect-error - msSaveBlob is not in the types
      navigator.msSaveBlob(blobObj, fileName);
    } else {
      link.addEventListener('click', () => {
        link.download = fileName;
        link.href = window.URL.createObjectURL(blobObj);
      });
    }
  }

  // trigger click
  if (link.click) {
    link.click();
  } else {
    const e = document.createEvent('MouseEvents');
    e.initEvent('click', false, false);
    link.dispatchEvent(e);
  }
}

export function formatLanguage(lan: string) {
  if (lan === 'en') {
    return 'en-US';
  } else if (lan === 'zh') {
    return 'zh-CN';
  }
  return lan;
}

export const TRANSCRIPT_CONTAINER_CLASS_NAME = 'ym-transcript-container';

export const safeDecodeURIComponent = (str: string): string => {
  try {
    return decodeURIComponent(str);
  } catch (e) {
    return str;
  }
};

export const PUNCTUATION_REGEX = /([.,!?]\s*|[，。！？、])/;
export const END_SENTENCE_PUNCTUATION = /[.,!?。，！？]/;
export const ENDED_WITH_PUNCTUATION = /[.,!?。，！？]$/;
export const CJK_PATTERN =
  /[\u3040-\u30ff\u3400-\u4dbf\u4e00-\u9fff\uf900-\ufaff\uff66-\uff9f，。！？、：；——～“”《》]/g;
const NON_ENDING_CHINESE_CHAR_PATTERN = /[，、]/;

export function addCJKSpacing(text: string): string {
  let result = '';
  let prevIsCJK = isCJKChar(text.charAt(0));

  for (let i = 0; i < text.length; i++) {
    const prevChar = i > 0 ? text.charAt(i - 1) : '';
    const char = text.charAt(i);
    const currentIsCJK = isCJKChar(char);

    if (
      i > 0 &&
      currentIsCJK !== prevIsCJK &&
      !NON_ENDING_CHINESE_CHAR_PATTERN.test(char) &&
      !NON_ENDING_CHINESE_CHAR_PATTERN.test(prevChar)
    ) {
      result += ' ';
    }

    result += char;
    prevIsCJK = currentIsCJK;
  }

  return result;
}

function isCJKChar(char: string): boolean {
  return /[\u3040-\u30ff\u3400-\u4dbf\u4e00-\u9fff\uf900-\ufaff\uff66-\uff9f，。！？、：；——～“”《》]/.test(
    char,
  );
}

export function selectLanguage(contents: TranscriptContent[], user?: User | null) {
  return (
    contents.find(({ language: lang }) => lang === user?.preference?.ai_response_language)
      ?.language ||
    contents.find(({ language: lang }) => lang === user?.preference?.ai_2nd_response_language)
      ?.language ||
    contents.find(({ language: lang }) => lang === user?.preference?.ai_response_language)
      ?.language ||
    contents.find(({ language: lang }) => lang === 'en-US')?.language ||
    (contents[0]?.language as string)
  );
}
