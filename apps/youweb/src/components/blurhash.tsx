import type { SnipVO } from '@repo/common/types/snip/app-types';
import { Blurhash as BlurhashUI } from '@repo/ui/components/custom/blurhash';

interface BlurhashProps extends React.HTMLAttributes<HTMLDivElement> {
  snip?: SnipVO;
  metadata?: ImageMetadata;
}

export interface ImageMetadata {
  width: number;
  height: number;
  blurhash: string;
}

export const Blurhash: React.FC<BlurhashProps> = ({ snip, metadata, className }) => {
  const { extra } = snip || {};

  if (extra) {
    try {
      metadata = JSON.parse(extra).hero_image_metadata;
    } catch {
      // ignore
    }
  }

  return <BlurhashUI metadata={metadata} className={className} />;
};
