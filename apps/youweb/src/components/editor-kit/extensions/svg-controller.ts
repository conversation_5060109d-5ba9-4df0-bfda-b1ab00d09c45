import { ISVGEditorController } from '@repo/ui-business-editor';
import { apiClient, callAPI } from '@/utils/callHTTP';

export class SVGEditorController implements ISVGEditorController {
  async uploadSVG(svg: string): Promise<string> {
    const { data, error } = await callAPI(apiClient.fileApi.uploadSvg({ svg }), { silent: true });
    if (error) {
      return '';
    }
    return data.image_url;
  }
}
