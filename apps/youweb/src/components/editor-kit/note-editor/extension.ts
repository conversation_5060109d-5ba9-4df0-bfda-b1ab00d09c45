import {
  getCommonFunctionExtensionList,
  getMarkExtensionList,
  getNodeExtensionList,
} from '@repo/ui-business-editor';
import { ImageUploadController } from '../extensions/image-upload-controller';
import { SVGEditorController } from '../extensions/svg-controller';

export const getNoteEditorExtension = () => {
  return [
    ...getNodeExtensionList({
      imageOptions: {
        imageUploadController: new ImageUploadController(),
      },
      svgOptions: {
        svgController: new SVGEditorController(),
      },
    }),
    ...getMarkExtensionList(),
    ...getCommonFunctionExtensionList({
      characterCountOptions: {
        limit: 30000,
      },
      placeholderOptions: {
        placeholder: 'Add a note...',
      },
    }),
  ];
};
