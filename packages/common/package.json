{"name": "@repo/common", "version": "0.0.0", "private": true, "license": "MIT", "files": ["./dist/**"], "publishConfig": {"access": "public"}, "exports": {".": {"types": "./dist/types/index.d.ts", "import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}, "./*": {"types": "./dist/types/*.d.ts", "import": "./dist/esm/*.js", "require": "./dist/cjs/*.js"}}, "scripts": {"build": "npm run build:clean && npm run build:esm && npm run build:cjs && npm run build:types && echo '✅ @repo/common: Build completed successfully!'", "build:clean": "rm -rf dist", "build:esm": "tsc -p tsconfig.esm.json && echo '{\"type\":\"module\"}' > dist/esm/package.json", "build:cjs": "tsc -p tsconfig.cjs.json && echo '{\"type\":\"commonjs\"}' > dist/cjs/package.json", "build:types": "tsc -p tsconfig.types.json", "tsc": "tsc", "tsc:clean": "tsc --build --clean", "tsc:check": "tsc --noEmit", "typecheck": "tsc --noEmit", "lint": "biome check . --diagnostic-level=error", "lint:fix": "biome check . --write --diagnostic-level=error", "format": "biome format . --write", "format:check": "biome format .", "test": "find ./test -name '*.test.ts' -exec node --import tsx --test {} \\;", "ci": "pnpm run lint:fix && pnpm run tsc:check && pnpm run test"}, "peerDependencies": {"normalize-url": "^8.0.1", "turndown": "^7.2.0", "zod": "catalog:", "zod-validation-error": "catalog:"}, "devDependencies": {"@types/node": "catalog:", "@types/turndown": "^5.0.5", "typescript": "catalog:"}, "dependencies": {}}