export const importDynamic = new Function('modulePath', 'return import(modulePath)');

export async function getNormalizedUrl(url: string) {
  const normalizeUrl = await importDynamic('normalize-url');
  return normalizeUrl(url, {
    stripWWW: false,
    stripProtocol: true,
    stripHash: true,
    removeQueryParameters: [
      /^utm_\w+/i,
      'gclid',
      'fbclid',
      'msclkid',
      'emci',
      'emdi',
      'ceid',
      'hootPostID',
      '__s',
      '_',
      /\w*session\w*/i,
    ],
  });
}

export const isYouTubeUrl = (url?: string) =>
  [
    'youtube.com/watch',
    'www.youtube.com/watch',
    'm.youtube.com/watch',
    'youtu.be',
    'www.youtu.be',
  ].some((prefix) => url?.includes(prefix));
export const isBilibiliUrl = (url?: string) => url?.includes('bilibili.com');
export const isTiktokUrl = (url?: string) => url?.includes('tiktok.com');
export const isWikipediaUrl = (url?: string) => url?.includes('wikipedia.org');
export const isMediumUrl = (url?: string) => url?.includes('medium.com');
export const isWeixinUrl = (url?: string) => url?.includes('mp.weixin.qq.com');
export const isXiaohongshuUrl = (url?: string) => url?.includes('xiaohongshu.com');
export const isXiaoyuzhouUrl = (url?: string) => url?.includes('xiaoyuzhoufm.com');
export const isApplePodcastUrl = (url?: string) => url?.includes('podcasts.apple.com');
export const isSpotifyUrl = (url?: string) => url?.includes('open.spotify.com');

export const isVideoUrl = (url?: string) =>
  isYouTubeUrl(url) || isBilibiliUrl(url) || isTiktokUrl(url);
