import { mergeAttributes, Node } from '@tiptap/core';

import { DIFF_CHANGE_ATTR_NAME } from '../const';

export type SVGOptions = {};

export interface SVGAttributes {
  src: string;
  alt: string;
  width: number;
  height: number;
}

const SVGExtensionName = 'SvgEditor';

export const SVG = Node.create<SVGOptions>({
  name: SVGExtensionName,

  group: 'block',

  atom: true,

  addAttributes() {
    return {
      src: {
        default: null,
        parseHTML: (element) => element.getAttribute('data-src'),
        renderHTML: (attributes) => {
          return { 'data-src': attributes.src };
        },
      },
      alt: {
        default: null,
        parseHTML: (element) => element.getAttribute('data-alt'),
        renderHTML: (attributes) => {
          return { 'data-alt': attributes.alt };
        },
      },
      [DIFF_CHANGE_ATTR_NAME]: {
        default: undefined,
        parseHTML: () => undefined,
        renderHTML: () => undefined,
      },
      // width: {
      //   default: undefined,
      //   parseHTML: (element) => Number(element.getAttribute("width")),
      //   renderHTML: (attributes) => ({
      //     width: Number(attributes.width),
      //   }),
      // },

      // height: {
      //   default: undefined,
      //   parseHTML: (element) => Number(element.getAttribute("height")),
      //   renderHTML: (attributes) => ({
      //     height: Number(attributes.height),
      //   }),
      // },
    };
  },

  addOptions() {
    return {
      HTMLAttributes: {},
    };
  },

  parseHTML() {
    return [
      {
        tag: `div[data-type="${SVGExtensionName}"]`,
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    return ['div', mergeAttributes({ 'data-type': SVGExtensionName }, HTMLAttributes)];
  },
});
