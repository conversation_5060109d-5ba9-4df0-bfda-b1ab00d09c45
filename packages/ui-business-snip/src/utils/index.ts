import { z } from 'zod';

// 定义配置的工厂函数
export function defineMaterialConfigs<
  TOptions extends z.ZodObject<z.ZodRawShape>,
  TEvents extends z.ZodObject<z.ZodRawShape>,
>(config: { options: TOptions; events: TEvents }) {
  return {
    schema: z
      .object({
        options: config.options.optional(),
        events: config.events.optional(),
      })
      .optional(),
    parseOptions: (input?: z.input<TOptions>): z.infer<TOptions> => {
      return config.options.parse(input || {});
    },
    parseEvents: (input?: z.input<TEvents>): z.infer<TEvents> => {
      return config.events.parse(input || {});
    },
    getDefaults: () => ({
      options: config.options.parse({}),
      events: config.events.parse({}),
    }),
  };
}
