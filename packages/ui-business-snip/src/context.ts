import { UserVO } from '@repo/common';
import { createContext, useContext } from 'react';
import { SnipConfig } from './config';

// 使用各处合并后的默认配置作为Context默认值
export const SnipContext = createContext<
  | {
      config: SnipConfig;
      user?: UserVO;
      isSmallScreen?: boolean;
      searchParams?: URLSearchParams;
    }
  | undefined
>(undefined);

export function useSnipContext() {
  const ctx = useContext(SnipContext);
  if (!ctx) {
    throw new Error('useSnipContext must be used within SnipProvider');
  }
  return ctx;
}
