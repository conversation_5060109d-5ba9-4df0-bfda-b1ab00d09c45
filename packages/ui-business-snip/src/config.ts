// config/snip-config-schema.ts

import { AllApiClients } from '@repo/api/generated-client/snake-case/index';
import { createCallHTTPStream } from '@repo/common/utilities/http';
import { z } from 'zod';
import { onlineVideoConfig } from './modules/online-video/config';

// 全局配置 Schema
export const SnipConfigSchema = z.object({
  // 目前先用 snake-case 的 api client，后续再统一重构为 camel-case 的 api client
  apiClient: z.custom<AllApiClients>((val) => typeof val === 'object'),

  callHTTPStream: z.custom<ReturnType<typeof createCallHTTPStream>>(
    (val) => typeof val === 'function',
  ),

  options: z
    .object({
      readonly: z.boolean().default(false),
    })
    .default({
      readonly: false,
    }),

  // 预留，后续用于配置组件，比如 ImagePreview，组件传入 props 需要有类型检测
  components: z.object({}).optional(),

  // 全局事件
  events: z
    .object({
      // 需要外部刷新 snipDetailAtom，否则请求后数据不会同步到组件中
      onNeedRefreshSnip: z
        .function()
        .returns(z.promise(z.void()))
        .default(() => async () => {}),
    })
    .optional(),

  services: z
    .object({
      deleteUrlParams: z
        .function(z.tuple([z.array(z.string())]))
        .returns(z.promise(z.void()))
        .default(async () => {}),
    })
    .optional(),

  onlineVideo: onlineVideoConfig.schema,
});

export type SnipConfigInput = z.input<typeof SnipConfigSchema>;

// 类型推导
export type SnipConfig = z.infer<typeof SnipConfigSchema>;

// 外部需要传入的配置（必需 + 可选覆盖）
// export const ExternalSnipConfigSchema = SnipConfigSchema.pick({
//   events: true,
//   apiClient: true,
//   httpStreamClient: true,
// }).extend({
//   options: SnipConfigSchema.shape.options.optional(),
// });

// export type ExternalSnipConfig = z.infer<typeof ExternalSnipConfigSchema>;

// 合并 externalConfig 和 defaultConfig
export function createConfig(externalConfig: SnipConfigInput): SnipConfig {
  return SnipConfigSchema.parse(externalConfig) as SnipConfig;
}
