{"name": "@repo/ui-business-snip", "version": "0.0.0", "private": true, "license": "MIT", "files": ["src"], "exports": {".": "./src/index.tsx"}, "scripts": {"build": "echo '✅ @repo/ui-business-snip: No build needed - TypeScript source files consumed directly'", "typecheck": "tsc --noEmit", "lint": "biome check . --diagnostic-level=error", "lint:fix": "biome check . --write --diagnostic-level=error", "format": "biome format . --write", "format:check": "biome format ."}, "dependencies": {"@repo/api": "workspace:*", "@repo/common": "workspace:*", "@repo/ui": "workspace:*", "@youmindinc/youcommon": "catalog:", "@youmindinc/youmind-translate": "0.2.0", "antd": "catalog:", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "copy-image-clipboard": "^2.1.2", "framer-motion": "catalog:", "howler": "catalog:", "immer": "^10.1.1", "jotai": "catalog:", "lucide-react": "catalog:", "marked": "^16.1.1", "next-themes": "^0.4.6", "posthog-js": "catalog:", "react": "catalog:", "react-dom": "catalog:", "react-hook-form": "catalog:", "react-youtube": "^10.1.0", "tailwind-merge": "^2.6.0", "use-debounce": "^10.0.5", "usehooks-ts": "catalog:", "zod": "catalog:"}, "devDependencies": {"@types/react": "catalog:", "@types/react-dom": "catalog:", "tailwindcss": "catalog:", "tailwindcss-animate": "^1.0.7", "typescript": "catalog:"}}