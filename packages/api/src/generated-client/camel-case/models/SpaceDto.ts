/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { CreditAccountDto } from './CreditAccountDto';
import {
  CreditAccountDtoFromJSON,
  CreditAccountDtoFromJSONTyped,
  CreditAccountDtoToJSON,
  CreditAccountDtoToJSONTyped,
} from './CreditAccountDto';
import type { SubscriptionDto } from './SubscriptionDto';
import {
  SubscriptionDtoFromJSON,
  SubscriptionDtoFromJSONTyped,
  SubscriptionDtoToJSON,
  SubscriptionDtoToJSONTyped,
} from './SubscriptionDto';

/**
 *
 * @export
 * @interface SpaceDto
 */
export interface SpaceDto {
  /**
   * 空间 ID
   * @type {string}
   * @memberof SpaceDto
   */
  id: string;
  /**
   * 创建时间
   * @type {Date}
   * @memberof SpaceDto
   */
  createdAt: Date;
  /**
   * 更新时间
   * @type {Date}
   * @memberof SpaceDto
   */
  updatedAt: Date;
  /**
   * 创建者 ID
   * @type {string}
   * @memberof SpaceDto
   */
  creatorId: string;
  /**
   * 积分账户
   * @type {CreditAccountDto}
   * @memberof SpaceDto
   */
  creditAccount: CreditAccountDto;
  /**
   * 订阅
   * @type {SubscriptionDto}
   * @memberof SpaceDto
   */
  subscription?: SubscriptionDto;
  /**
   * 状态
   * @type {string}
   * @memberof SpaceDto
   * @deprecated
   */
  status: string;
  /**
   * 试用结束时间
   * @type {Date}
   * @memberof SpaceDto
   * @deprecated
   */
  trialExpiresAt: Date;
}

/**
 * Check if a given object implements the SpaceDto interface.
 */
export function instanceOfSpaceDto(value: object): value is SpaceDto {
  if (!('id' in value) || value.id === undefined) return false;
  if (!('createdAt' in value) || value.createdAt === undefined) return false;
  if (!('updatedAt' in value) || value.updatedAt === undefined) return false;
  if (!('creatorId' in value) || value.creatorId === undefined) return false;
  if (!('creditAccount' in value) || value.creditAccount === undefined) return false;
  if (!('status' in value) || value.status === undefined) return false;
  if (!('trialExpiresAt' in value) || value.trialExpiresAt === undefined) return false;
  return true;
}

export function SpaceDtoFromJSON(json: any): SpaceDto {
  return SpaceDtoFromJSONTyped(json, false);
}

export function SpaceDtoFromJSONTyped(json: any, _ignoreDiscriminator: boolean): SpaceDto {
  if (json == null) {
    return json;
  }
  return {
    id: json.id,
    createdAt: new Date(json.createdAt),
    updatedAt: new Date(json.updatedAt),
    creatorId: json.creatorId,
    creditAccount: CreditAccountDtoFromJSON(json.creditAccount),
    subscription:
      json.subscription == null ? undefined : SubscriptionDtoFromJSON(json.subscription),
    status: json.status,
    trialExpiresAt: new Date(json.trialExpiresAt),
  };
}

export function SpaceDtoToJSON(json: any): SpaceDto {
  return SpaceDtoToJSONTyped(json, false);
}

export function SpaceDtoToJSONTyped(
  value?: SpaceDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    id: value.id,
    createdAt: value.createdAt.toISOString(),
    updatedAt: value.updatedAt.toISOString(),
    creatorId: value.creatorId,
    creditAccount: CreditAccountDtoToJSON(value.creditAccount),
    subscription: SubscriptionDtoToJSON(value.subscription),
    status: value.status,
    trialExpiresAt: value.trialExpiresAt.toISOString(),
  };
}
