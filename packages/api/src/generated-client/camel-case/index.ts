/* tslint:disable */
/* eslint-disable */

export * from './apis/index';
export * from './models/index';

// This file is generated by generate.js and provides a unified entry point for all API classes
// DO NOT MODIFY DIRECTLY
import { Configuration } from '../../runtime';
import { AIApi } from './apis/AIApi';
import { AppApi } from './apis/AppApi';
import { AuthApi } from './apis/AuthApi';
import { BoardApi } from './apis/BoardApi';
import { BoardGroupApi } from './apis/BoardGroupApi';
import { BoardItemApi } from './apis/BoardItemApi';
import { ChatShortcutApi } from './apis/ChatShortcutApi';
import { ChatV1Api } from './apis/ChatV1Api';
import { ChatV2Api } from './apis/ChatV2Api';
import { CreditManagementAPIApi } from './apis/CreditManagementAPIApi';
import { DiffApi } from './apis/DiffApi';
import { EntityApi } from './apis/EntityApi';
import { FavoritesApi } from './apis/FavoritesApi';
import { FileApi } from './apis/FileApi';
import { HealthApi } from './apis/HealthApi';
import { MaterialApi } from './apis/MaterialApi';
import { NoteApi } from './apis/NoteApi';
import { PlaylistItemApi } from './apis/PlaylistItemApi';
import { PositionApi } from './apis/PositionApi';
import { SearchApi } from './apis/SearchApi';
import { ShareApi } from './apis/ShareApi';
import { ShortLinkApi } from './apis/ShortLinkApi';
import { SnipApi } from './apis/SnipApi';
import { StreamingTestApi } from './apis/StreamingTestApi';
import { SubscriptionApi } from './apis/SubscriptionApi';
import { TextApi } from './apis/TextApi';
import { ThoughtApi } from './apis/ThoughtApi';
import { ThoughtVersionApi } from './apis/ThoughtVersionApi';
import { UsageRecordApi } from './apis/UsageRecordApi';
import { UserApi } from './apis/UserApi';
import { UserPreferenceApi } from './apis/UserPreferenceApi';
import { WebhookApi } from './apis/WebhookApi';

export interface AllApiClients {
  aIApi: AIApi;
  appApi: AppApi;
  authApi: AuthApi;
  boardApi: BoardApi;
  boardGroupApi: BoardGroupApi;
  boardItemApi: BoardItemApi;
  chatShortcutApi: ChatShortcutApi;
  chatV1Api: ChatV1Api;
  chatV2Api: ChatV2Api;
  creditManagementAPIApi: CreditManagementAPIApi;
  diffApi: DiffApi;
  entityApi: EntityApi;
  favoritesApi: FavoritesApi;
  fileApi: FileApi;
  healthApi: HealthApi;
  materialApi: MaterialApi;
  noteApi: NoteApi;
  playlistItemApi: PlaylistItemApi;
  positionApi: PositionApi;
  searchApi: SearchApi;
  shareApi: ShareApi;
  shortLinkApi: ShortLinkApi;
  snipApi: SnipApi;
  streamingTestApi: StreamingTestApi;
  subscriptionApi: SubscriptionApi;
  textApi: TextApi;
  thoughtApi: ThoughtApi;
  thoughtVersionApi: ThoughtVersionApi;
  usageRecordApi: UsageRecordApi;
  userApi: UserApi;
  userPreferenceApi: UserPreferenceApi;
  webhookApi: WebhookApi;
}

export function createAllApiClients(configuration: Configuration): AllApiClients {
  return {
    aIApi: new AIApi(configuration),
    appApi: new AppApi(configuration),
    authApi: new AuthApi(configuration),
    boardApi: new BoardApi(configuration),
    boardGroupApi: new BoardGroupApi(configuration),
    boardItemApi: new BoardItemApi(configuration),
    chatShortcutApi: new ChatShortcutApi(configuration),
    chatV1Api: new ChatV1Api(configuration),
    chatV2Api: new ChatV2Api(configuration),
    creditManagementAPIApi: new CreditManagementAPIApi(configuration),
    diffApi: new DiffApi(configuration),
    entityApi: new EntityApi(configuration),
    favoritesApi: new FavoritesApi(configuration),
    fileApi: new FileApi(configuration),
    healthApi: new HealthApi(configuration),
    materialApi: new MaterialApi(configuration),
    noteApi: new NoteApi(configuration),
    playlistItemApi: new PlaylistItemApi(configuration),
    positionApi: new PositionApi(configuration),
    searchApi: new SearchApi(configuration),
    shareApi: new ShareApi(configuration),
    shortLinkApi: new ShortLinkApi(configuration),
    snipApi: new SnipApi(configuration),
    streamingTestApi: new StreamingTestApi(configuration),
    subscriptionApi: new SubscriptionApi(configuration),
    textApi: new TextApi(configuration),
    thoughtApi: new ThoughtApi(configuration),
    thoughtVersionApi: new ThoughtVersionApi(configuration),
    usageRecordApi: new UsageRecordApi(configuration),
    userApi: new UserApi(configuration),
    userPreferenceApi: new UserPreferenceApi(configuration),
    webhookApi: new WebhookApi(configuration),
  };
}
