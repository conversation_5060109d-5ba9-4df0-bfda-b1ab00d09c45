/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import * as runtime from '../../../runtime';
import type {
  GetUserDto,
  InitCurrentUserDto,
  InitUsersDto,
  UpdateUserAvatarDto,
  UpdateUserNameDto,
  UpdateUserTimeZoneIfNotSetDto,
  UserDto,
  UserWithPreferenceSpaceDto,
} from '../models/index';
import {
  GetUserDtoFromJSON,
  GetUserDtoToJSON,
  InitCurrentUserDtoFromJSON,
  InitCurrentUserDtoToJSON,
  InitUsersDtoFromJSON,
  InitUsersDtoToJSON,
  UpdateUserAvatarDtoFromJSON,
  UpdateUserAvatarDtoToJSON,
  UpdateUserNameDtoFromJSON,
  UpdateUserNameDtoToJSON,
  UpdateUserTimeZoneIfNotSetDtoFromJSON,
  UpdateUserTimeZoneIfNotSetDtoToJSON,
  UserDtoFromJSON,
  UserDtoToJSON,
  UserWithPreferenceSpaceDtoFromJSON,
  UserWithPreferenceSpaceDtoToJSON,
} from '../models/index';

export interface UserControllerGetUserRequest {
  getUserDto: GetUserDto;
}

export interface UserControllerInitCurrentUserRequest {
  initCurrentUserDto: InitCurrentUserDto;
}

export interface UserControllerInitUsersRequest {
  initUsersDto: InitUsersDto;
}

export interface UserControllerUpdateTimeZoneIfNotSetRequest {
  updateUserTimeZoneIfNotSetDto: UpdateUserTimeZoneIfNotSetDto;
}

export interface UserControllerUpdateUserAvatarRequest {
  updateUserAvatarDto: UpdateUserAvatarDto;
}

export interface UserControllerUpdateUserNameRequest {
  updateUserNameDto: UpdateUserNameDto;
}

/**
 * UserApi - interface
 *
 * @export
 * @interface UserApiInterface
 */
export interface UserApiInterface {
  /**
   * 删除当前登录的用户账户
   * @summary 删除当前用户
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UserApiInterface
   */
  deleteCurrentUserRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<void>>;

  /**
   * 删除当前登录的用户账户
   * 删除当前用户
   */
  deleteCurrentUser(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<void>;

  /**
   * 获取当前登录用户的详细信息
   * @summary 获取当前用户
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UserApiInterface
   */
  getCurrentUserRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<UserWithPreferenceSpaceDto>>;

  /**
   * 获取当前登录用户的详细信息
   * 获取当前用户
   */
  getCurrentUser(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<UserWithPreferenceSpaceDto>;

  /**
   * 获取用户
   * @summary 获取用户
   * @param {GetUserDto} getUserDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UserApiInterface
   */
  getUserRaw(
    requestParameters: UserControllerGetUserRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<void>>;

  /**
   * 获取用户
   * 获取用户
   */
  getUser(
    getUserDto: GetUserDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<void>;

  /**
   * 初始化当前用户的数据和设置
   * @summary 初始化当前用户
   * @param {InitCurrentUserDto} initCurrentUserDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UserApiInterface
   */
  initCurrentUserRaw(
    requestParameters: UserControllerInitCurrentUserRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<UserWithPreferenceSpaceDto>>;

  /**
   * 初始化当前用户的数据和设置
   * 初始化当前用户
   */
  initCurrentUser(
    initCurrentUserDto: InitCurrentUserDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<UserWithPreferenceSpaceDto>;

  /**
   * 初始化存量用户
   * @summary 初始化存量用户
   * @param {InitUsersDto} initUsersDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UserApiInterface
   */
  initUsersRaw(
    requestParameters: UserControllerInitUsersRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<void>>;

  /**
   * 初始化存量用户
   * 初始化存量用户
   */
  initUsers(
    initUsersDto: InitUsersDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<void>;

  /**
   * 如果用户时区未设置，则设置用户时区
   * @summary 设置用户时区
   * @param {UpdateUserTimeZoneIfNotSetDto} updateUserTimeZoneIfNotSetDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UserApiInterface
   */
  updateTimeZoneIfNotSetRaw(
    requestParameters: UserControllerUpdateTimeZoneIfNotSetRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<void>>;

  /**
   * 如果用户时区未设置，则设置用户时区
   * 设置用户时区
   */
  updateTimeZoneIfNotSet(
    updateUserTimeZoneIfNotSetDto: UpdateUserTimeZoneIfNotSetDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<void>;

  /**
   * 更新当前用户的头像
   * @summary 更新用户头像
   * @param {UpdateUserAvatarDto} updateUserAvatarDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UserApiInterface
   */
  updateUserAvatarRaw(
    requestParameters: UserControllerUpdateUserAvatarRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<UserDto>>;

  /**
   * 更新当前用户的头像
   * 更新用户头像
   */
  updateUserAvatar(
    updateUserAvatarDto: UpdateUserAvatarDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<UserDto>;

  /**
   * 更新当前用户的姓名
   * @summary 更新用户姓名
   * @param {UpdateUserNameDto} updateUserNameDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof UserApiInterface
   */
  updateUserNameRaw(
    requestParameters: UserControllerUpdateUserNameRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<UserDto>>;

  /**
   * 更新当前用户的姓名
   * 更新用户姓名
   */
  updateUserName(
    updateUserNameDto: UpdateUserNameDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<UserDto>;
}

/**
 *
 */
export class UserApi extends runtime.BaseAPI implements UserApiInterface {
  /**
   * 删除当前登录的用户账户
   * 删除当前用户
   */
  async deleteCurrentUserRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<void>> {
    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    const urlPath = `/api/v1/user/deleteCurrentUser`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
      },
      initOverrides,
    );

    return new runtime.VoidApiResponse(response);
  }

  /**
   * 删除当前登录的用户账户
   * 删除当前用户
   */
  async deleteCurrentUser(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<void> {
    await this.deleteCurrentUserRaw(initOverrides);
  }

  /**
   * 获取当前登录用户的详细信息
   * 获取当前用户
   */
  async getCurrentUserRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<UserWithPreferenceSpaceDto>> {
    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    const urlPath = `/api/v1/getCurrentUser`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) =>
      UserWithPreferenceSpaceDtoFromJSON(jsonValue),
    );
  }

  /**
   * 获取当前登录用户的详细信息
   * 获取当前用户
   */
  async getCurrentUser(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<UserWithPreferenceSpaceDto> {
    const response = await this.getCurrentUserRaw(initOverrides);
    return await response.value();
  }

  /**
   * 获取用户
   * 获取用户
   */
  async getUserRaw(
    requestParameters: UserControllerGetUserRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<void>> {
    if (requestParameters.getUserDto == null) {
      throw new runtime.RequiredError(
        'getUserDto',
        'Required parameter "getUserDto" was null or undefined when calling getUser().',
      );
    }

    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    headerParameters['Content-Type'] = 'application/json';

    const urlPath = `/api/v1/user/getUser`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
        body: GetUserDtoToJSON(requestParameters.getUserDto),
      },
      initOverrides,
    );

    return new runtime.VoidApiResponse(response);
  }

  /**
   * 获取用户
   * 获取用户
   */
  async getUser(
    getUserDto: GetUserDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<void> {
    await this.getUserRaw({ getUserDto: getUserDto }, initOverrides);
  }

  /**
   * 初始化当前用户的数据和设置
   * 初始化当前用户
   */
  async initCurrentUserRaw(
    requestParameters: UserControllerInitCurrentUserRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<UserWithPreferenceSpaceDto>> {
    if (requestParameters.initCurrentUserDto == null) {
      throw new runtime.RequiredError(
        'initCurrentUserDto',
        'Required parameter "initCurrentUserDto" was null or undefined when calling initCurrentUser().',
      );
    }

    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    headerParameters['Content-Type'] = 'application/json';

    const urlPath = `/api/v1/user/initCurrentUser`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
        body: InitCurrentUserDtoToJSON(requestParameters.initCurrentUserDto),
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) =>
      UserWithPreferenceSpaceDtoFromJSON(jsonValue),
    );
  }

  /**
   * 初始化当前用户的数据和设置
   * 初始化当前用户
   */
  async initCurrentUser(
    initCurrentUserDto: InitCurrentUserDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<UserWithPreferenceSpaceDto> {
    const response = await this.initCurrentUserRaw(
      { initCurrentUserDto: initCurrentUserDto },
      initOverrides,
    );
    return await response.value();
  }

  /**
   * 初始化存量用户
   * 初始化存量用户
   */
  async initUsersRaw(
    requestParameters: UserControllerInitUsersRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<void>> {
    if (requestParameters.initUsersDto == null) {
      throw new runtime.RequiredError(
        'initUsersDto',
        'Required parameter "initUsersDto" was null or undefined when calling initUsers().',
      );
    }

    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    headerParameters['Content-Type'] = 'application/json';

    const urlPath = `/api/v1/user/initUsers`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
        body: InitUsersDtoToJSON(requestParameters.initUsersDto),
      },
      initOverrides,
    );

    return new runtime.VoidApiResponse(response);
  }

  /**
   * 初始化存量用户
   * 初始化存量用户
   */
  async initUsers(
    initUsersDto: InitUsersDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<void> {
    await this.initUsersRaw({ initUsersDto: initUsersDto }, initOverrides);
  }

  /**
   * 如果用户时区未设置，则设置用户时区
   * 设置用户时区
   */
  async updateTimeZoneIfNotSetRaw(
    requestParameters: UserControllerUpdateTimeZoneIfNotSetRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<void>> {
    if (requestParameters.updateUserTimeZoneIfNotSetDto == null) {
      throw new runtime.RequiredError(
        'updateUserTimeZoneIfNotSetDto',
        'Required parameter "updateUserTimeZoneIfNotSetDto" was null or undefined when calling updateTimeZoneIfNotSet().',
      );
    }

    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    headerParameters['Content-Type'] = 'application/json';

    const urlPath = `/api/v1/user/setUserTimeZoneIfNotSet`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
        body: UpdateUserTimeZoneIfNotSetDtoToJSON(requestParameters.updateUserTimeZoneIfNotSetDto),
      },
      initOverrides,
    );

    return new runtime.VoidApiResponse(response);
  }

  /**
   * 如果用户时区未设置，则设置用户时区
   * 设置用户时区
   */
  async updateTimeZoneIfNotSet(
    updateUserTimeZoneIfNotSetDto: UpdateUserTimeZoneIfNotSetDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<void> {
    await this.updateTimeZoneIfNotSetRaw(
      { updateUserTimeZoneIfNotSetDto: updateUserTimeZoneIfNotSetDto },
      initOverrides,
    );
  }

  /**
   * 更新当前用户的头像
   * 更新用户头像
   */
  async updateUserAvatarRaw(
    requestParameters: UserControllerUpdateUserAvatarRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<UserDto>> {
    if (requestParameters.updateUserAvatarDto == null) {
      throw new runtime.RequiredError(
        'updateUserAvatarDto',
        'Required parameter "updateUserAvatarDto" was null or undefined when calling updateUserAvatar().',
      );
    }

    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    headerParameters['Content-Type'] = 'application/json';

    const urlPath = `/api/v1/user/patchUserAvatar`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
        body: UpdateUserAvatarDtoToJSON(requestParameters.updateUserAvatarDto),
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) => UserDtoFromJSON(jsonValue));
  }

  /**
   * 更新当前用户的头像
   * 更新用户头像
   */
  async updateUserAvatar(
    updateUserAvatarDto: UpdateUserAvatarDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<UserDto> {
    const response = await this.updateUserAvatarRaw(
      { updateUserAvatarDto: updateUserAvatarDto },
      initOverrides,
    );
    return await response.value();
  }

  /**
   * 更新当前用户的姓名
   * 更新用户姓名
   */
  async updateUserNameRaw(
    requestParameters: UserControllerUpdateUserNameRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<UserDto>> {
    if (requestParameters.updateUserNameDto == null) {
      throw new runtime.RequiredError(
        'updateUserNameDto',
        'Required parameter "updateUserNameDto" was null or undefined when calling updateUserName().',
      );
    }

    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    headerParameters['Content-Type'] = 'application/json';

    const urlPath = `/api/v1/user/patchUserName`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
        body: UpdateUserNameDtoToJSON(requestParameters.updateUserNameDto),
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) => UserDtoFromJSON(jsonValue));
  }

  /**
   * 更新当前用户的姓名
   * 更新用户姓名
   */
  async updateUserName(
    updateUserNameDto: UpdateUserNameDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<UserDto> {
    const response = await this.updateUserNameRaw(
      { updateUserNameDto: updateUserNameDto },
      initOverrides,
    );
    return await response.value();
  }
}
