/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON><PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { CreditAccountDto } from './CreditAccountDto';
import {
  CreditAccountDtoFromJSON,
  CreditAccountDtoFromJSONTyped,
  CreditAccountDtoToJSON,
  CreditAccountDtoToJSONTyped,
} from './CreditAccountDto';
import type { SubscriptionDto } from './SubscriptionDto';
import {
  SubscriptionDtoFromJSON,
  SubscriptionDtoFromJSONTyped,
  SubscriptionDtoToJSON,
  SubscriptionDtoToJSONTyped,
} from './SubscriptionDto';

/**
 *
 * @export
 * @interface SpaceDto
 */
export interface SpaceDto {
  /**
   * 空间 ID
   * @type {string}
   * @memberof SpaceDto
   */
  id: string;
  /**
   * 创建时间
   * @type {Date}
   * @memberof SpaceDto
   */
  created_at: Date;
  /**
   * 更新时间
   * @type {Date}
   * @memberof SpaceDto
   */
  updated_at: Date;
  /**
   * 创建者 ID
   * @type {string}
   * @memberof SpaceDto
   */
  creator_id: string;
  /**
   * 积分账户
   * @type {CreditAccountDto}
   * @memberof SpaceDto
   */
  credit_account: CreditAccountDto;
  /**
   * 订阅
   * @type {SubscriptionDto}
   * @memberof SpaceDto
   */
  subscription?: SubscriptionDto;
  /**
   * 状态
   * @type {string}
   * @memberof SpaceDto
   * @deprecated
   */
  status: string;
  /**
   * 试用结束时间
   * @type {Date}
   * @memberof SpaceDto
   * @deprecated
   */
  trial_expires_at: Date;
}

/**
 * Check if a given object implements the SpaceDto interface.
 */
export function instanceOfSpaceDto(value: object): value is SpaceDto {
  if (!('id' in value) || value.id === undefined) return false;
  if (!('created_at' in value) || value.created_at === undefined) return false;
  if (!('updated_at' in value) || value.updated_at === undefined) return false;
  if (!('creator_id' in value) || value.creator_id === undefined) return false;
  if (!('credit_account' in value) || value.credit_account === undefined) return false;
  if (!('status' in value) || value.status === undefined) return false;
  if (!('trial_expires_at' in value) || value.trial_expires_at === undefined) return false;
  return true;
}

export function SpaceDtoFromJSON(json: any): SpaceDto {
  return SpaceDtoFromJSONTyped(json, false);
}

export function SpaceDtoFromJSONTyped(json: any, _ignoreDiscriminator: boolean): SpaceDto {
  if (json == null) {
    return json;
  }
  return {
    id: json.id,
    created_at: new Date(json.created_at),
    updated_at: new Date(json.updated_at),
    creator_id: json.creator_id,
    credit_account: CreditAccountDtoFromJSON(json.credit_account),
    subscription:
      json.subscription == null ? undefined : SubscriptionDtoFromJSON(json.subscription),
    status: json.status,
    trial_expires_at: new Date(json.trial_expires_at),
  };
}

export function SpaceDtoToJSON(json: any): SpaceDto {
  return SpaceDtoToJSONTyped(json, false);
}

export function SpaceDtoToJSONTyped(
  value?: SpaceDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    id: value.id,
    created_at: value.created_at.toISOString(),
    updated_at: value.updated_at.toISOString(),
    creator_id: value.creator_id,
    credit_account: CreditAccountDtoToJSON(value.credit_account),
    subscription: SubscriptionDtoToJSON(value.subscription),
    status: value.status,
    trial_expires_at: value.trial_expires_at.toISOString(),
  };
}
