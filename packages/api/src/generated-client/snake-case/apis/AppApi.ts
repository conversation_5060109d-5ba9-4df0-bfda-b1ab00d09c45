/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON><PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import * as runtime from '../../../runtime';

/**
 * AppApi - interface
 *
 * @export
 * @interface AppApiInterface
 */
export interface AppApiInterface {
  /**
   *
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof AppApiInterface
   */
  apiRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<void>>;

  /**
   */
  api(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<void>;

  /**
   *
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof AppApiInterface
   */
  apiIndexRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<void>>;

  /**
   */
  apiIndex(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<void>;

  /**
   *
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof AppApiInterface
   */
  apiSnakeCaseRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<void>>;

  /**
   */
  apiSnakeCase(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<void>;

  /**
   *
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof AppApiInterface
   */
  helloRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<void>>;

  /**
   */
  hello(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<void>;

  /**
   *
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof AppApiInterface
   */
  rootRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<void>>;

  /**
   */
  root(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<void>;
}

/**
 *
 */
export class AppApi extends runtime.BaseAPI implements AppApiInterface {
  /**
   */
  async apiRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<void>> {
    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    const urlPath = `/api/camel_case`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'GET',
        headers: headerParameters,
        query: queryParameters,
      },
      initOverrides,
    );

    return new runtime.VoidApiResponse(response);
  }

  /**
   */
  async api(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<void> {
    await this.apiRaw(initOverrides);
  }

  /**
   */
  async apiIndexRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<void>> {
    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    const urlPath = `/api`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'GET',
        headers: headerParameters,
        query: queryParameters,
      },
      initOverrides,
    );

    return new runtime.VoidApiResponse(response);
  }

  /**
   */
  async apiIndex(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<void> {
    await this.apiIndexRaw(initOverrides);
  }

  /**
   */
  async apiSnakeCaseRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<void>> {
    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    const urlPath = `/api/snake_case`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'GET',
        headers: headerParameters,
        query: queryParameters,
      },
      initOverrides,
    );

    return new runtime.VoidApiResponse(response);
  }

  /**
   */
  async apiSnakeCase(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<void> {
    await this.apiSnakeCaseRaw(initOverrides);
  }

  /**
   */
  async helloRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<void>> {
    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    const urlPath = `/hello`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'GET',
        headers: headerParameters,
        query: queryParameters,
      },
      initOverrides,
    );

    return new runtime.VoidApiResponse(response);
  }

  /**
   */
  async hello(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<void> {
    await this.helloRaw(initOverrides);
  }

  /**
   */
  async rootRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<void>> {
    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    const urlPath = `/`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'GET',
        headers: headerParameters,
        query: queryParameters,
      },
      initOverrides,
    );

    return new runtime.VoidApiResponse(response);
  }

  /**
   */
  async root(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<void> {
    await this.rootRaw(initOverrides);
  }
}
