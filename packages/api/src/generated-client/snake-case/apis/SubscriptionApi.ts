/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON><PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import * as runtime from '../../../runtime';
import type {
  CreateSubscriptionDto,
  RedirectResponseDto,
  SubscriptionDto,
  UpdateSubscriptionDto,
  UserWithPreferenceSpaceDto,
  VerifyTransactionDto,
} from '../models/index';
import {
  CreateSubscriptionDtoFromJSON,
  CreateSubscriptionDtoToJSON,
  RedirectResponseDtoFromJSON,
  RedirectResponseDtoToJSON,
  SubscriptionDtoFromJSON,
  SubscriptionDtoToJSON,
  UpdateSubscriptionDtoFromJSON,
  UpdateSubscriptionDtoToJSON,
  UserWithPreferenceSpaceDtoFromJSON,
  UserWithPreferenceSpaceDtoToJSON,
  VerifyTransactionDtoFromJSON,
  VerifyTransactionDtoToJSON,
} from '../models/index';

export interface SubscriptionControllerCreateSubscriptionRequest {
  createSubscriptionDto: CreateSubscriptionDto;
}

export interface SubscriptionControllerHandleStripeWebhookRequest {
  stripeSignature: string;
}

export interface SubscriptionControllerUpdateSubscriptionRequest {
  updateSubscriptionDto: UpdateSubscriptionDto;
}

export interface SubscriptionControllerVerifyTransactionRequest {
  verifyTransactionDto: VerifyTransactionDto;
}

/**
 * SubscriptionApi - interface
 *
 * @export
 * @interface SubscriptionApiInterface
 */
export interface SubscriptionApiInterface {
  /**
   * 用户主动取消付费订阅，设置为当前周期结束时生效
   * @summary 取消订阅
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof SubscriptionApiInterface
   */
  cancelSubscriptionRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<void>>;

  /**
   * 用户主动取消付费订阅，设置为当前周期结束时生效
   * 取消订阅
   */
  cancelSubscription(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<void>;

  /**
   *
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof SubscriptionApiInterface
   */
  checkoutSuccessRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<void>>;

  /**
   */
  checkoutSuccess(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<void>;

  /**
   * 为当前用户创建 Stripe Billing Portal 会话，用户可以在 Billing Portal 中管理订阅、更新支付方式、查看账单历史等
   * @summary 创建 Stripe Billing Portal 会话
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof SubscriptionApiInterface
   */
  createBillingPortalSessionRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<RedirectResponseDto>>;

  /**
   * 为当前用户创建 Stripe Billing Portal 会话，用户可以在 Billing Portal 中管理订阅、更新支付方式、查看账单历史等
   * 创建 Stripe Billing Portal 会话
   */
  createBillingPortalSession(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<RedirectResponseDto>;

  /**
   * 免费用户创建付费订阅，包括 Stripe Customer 创建、订阅创建和积分重置
   * @summary 创建付费订阅
   * @param {CreateSubscriptionDto} createSubscriptionDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof SubscriptionApiInterface
   */
  createSubscriptionRaw(
    requestParameters: SubscriptionControllerCreateSubscriptionRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<void>>;

  /**
   * 免费用户创建付费订阅，包括 Stripe Customer 创建、订阅创建和积分重置
   * 创建付费订阅
   */
  createSubscription(
    createSubscriptionDto: CreateSubscriptionDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<void>;

  /**
   * 删除订阅，用于测试
   * @summary 删除订阅，用于测试
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof SubscriptionApiInterface
   */
  deleteSubscriptionForTestRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<void>>;

  /**
   * 删除订阅，用于测试
   * 删除订阅，用于测试
   */
  deleteSubscriptionForTest(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<void>;

  /**
   * Query and return current user subscription information
   * @summary Find current user subscription
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof SubscriptionApiInterface
   */
  findSubscriptionRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<SubscriptionDto>>;

  /**
   * Query and return current user subscription information
   * Find current user subscription
   */
  findSubscription(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<SubscriptionDto>;

  /**
   * 处理来自 Apple App Store 的服务器通知事件（生产环境）
   * @summary 处理 Apple App Store Server Notifications
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof SubscriptionApiInterface
   */
  handleAppleNotificationRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<void>>;

  /**
   * 处理来自 Apple App Store 的服务器通知事件（生产环境）
   * 处理 Apple App Store Server Notifications
   */
  handleAppleNotification(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<void>;

  /**
   * 处理来自 Apple App Store 的服务器通知事件（沙盒环境）
   * @summary 处理 Apple App Store Server Notifications (Sandbox)
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof SubscriptionApiInterface
   */
  handleAppleSandboxNotificationRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<void>>;

  /**
   * 处理来自 Apple App Store 的服务器通知事件（沙盒环境）
   * 处理 Apple App Store Server Notifications (Sandbox)
   */
  handleAppleSandboxNotification(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<void>;

  /**
   * 接收并处理 Stripe 的 webhook 事件
   * @summary 处理 Stripe Webhook
   * @param {string} stripeSignature
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof SubscriptionApiInterface
   */
  handleStripeWebhookRaw(
    requestParameters: SubscriptionControllerHandleStripeWebhookRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<void>>;

  /**
   * 接收并处理 Stripe 的 webhook 事件
   * 处理 Stripe Webhook
   */
  handleStripeWebhook(
    stripeSignature: string,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<void>;

  /**
   * 用户主动变更订阅的产品等级和计费周期，支持升级、降级和周期变更
   * @summary 变更订阅配置
   * @param {UpdateSubscriptionDto} updateSubscriptionDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof SubscriptionApiInterface
   */
  updateSubscriptionRaw(
    requestParameters: SubscriptionControllerUpdateSubscriptionRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<void>>;

  /**
   * 用户主动变更订阅的产品等级和计费周期，支持升级、降级和周期变更
   * 变更订阅配置
   */
  updateSubscription(
    updateSubscriptionDto: UpdateSubscriptionDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<void>;

  /**
   * 验证 Apple 内购交易的有效性，确认交易属于当前用户，并更新用户的订阅状态
   * @summary 验证 Apple 内购交易
   * @param {VerifyTransactionDto} verifyTransactionDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof SubscriptionApiInterface
   */
  verifyTransactionRaw(
    requestParameters: SubscriptionControllerVerifyTransactionRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<UserWithPreferenceSpaceDto>>;

  /**
   * 验证 Apple 内购交易的有效性，确认交易属于当前用户，并更新用户的订阅状态
   * 验证 Apple 内购交易
   */
  verifyTransaction(
    verifyTransactionDto: VerifyTransactionDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<UserWithPreferenceSpaceDto>;
}

/**
 *
 */
export class SubscriptionApi extends runtime.BaseAPI implements SubscriptionApiInterface {
  /**
   * 用户主动取消付费订阅，设置为当前周期结束时生效
   * 取消订阅
   */
  async cancelSubscriptionRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<void>> {
    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    const urlPath = `/api/v1/subscription/cancelSubscription`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
      },
      initOverrides,
    );

    return new runtime.VoidApiResponse(response);
  }

  /**
   * 用户主动取消付费订阅，设置为当前周期结束时生效
   * 取消订阅
   */
  async cancelSubscription(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<void> {
    await this.cancelSubscriptionRaw(initOverrides);
  }

  /**
   */
  async checkoutSuccessRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<void>> {
    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    const urlPath = `/checkout-success`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'GET',
        headers: headerParameters,
        query: queryParameters,
      },
      initOverrides,
    );

    return new runtime.VoidApiResponse(response);
  }

  /**
   */
  async checkoutSuccess(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<void> {
    await this.checkoutSuccessRaw(initOverrides);
  }

  /**
   * 为当前用户创建 Stripe Billing Portal 会话，用户可以在 Billing Portal 中管理订阅、更新支付方式、查看账单历史等
   * 创建 Stripe Billing Portal 会话
   */
  async createBillingPortalSessionRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<RedirectResponseDto>> {
    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    const urlPath = `/api/v1/subscription/createBillingPortalSession`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) =>
      RedirectResponseDtoFromJSON(jsonValue),
    );
  }

  /**
   * 为当前用户创建 Stripe Billing Portal 会话，用户可以在 Billing Portal 中管理订阅、更新支付方式、查看账单历史等
   * 创建 Stripe Billing Portal 会话
   */
  async createBillingPortalSession(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<RedirectResponseDto> {
    const response = await this.createBillingPortalSessionRaw(initOverrides);
    return await response.value();
  }

  /**
   * 免费用户创建付费订阅，包括 Stripe Customer 创建、订阅创建和积分重置
   * 创建付费订阅
   */
  async createSubscriptionRaw(
    requestParameters: SubscriptionControllerCreateSubscriptionRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<void>> {
    if (requestParameters.createSubscriptionDto == null) {
      throw new runtime.RequiredError(
        'createSubscriptionDto',
        'Required parameter "createSubscriptionDto" was null or undefined when calling createSubscription().',
      );
    }

    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    headerParameters['Content-Type'] = 'application/json';

    const urlPath = `/api/v1/subscription/createSubscription`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
        body: CreateSubscriptionDtoToJSON(requestParameters.createSubscriptionDto),
      },
      initOverrides,
    );

    return new runtime.VoidApiResponse(response);
  }

  /**
   * 免费用户创建付费订阅，包括 Stripe Customer 创建、订阅创建和积分重置
   * 创建付费订阅
   */
  async createSubscription(
    createSubscriptionDto: CreateSubscriptionDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<void> {
    await this.createSubscriptionRaw(
      { createSubscriptionDto: createSubscriptionDto },
      initOverrides,
    );
  }

  /**
   * 删除订阅，用于测试
   * 删除订阅，用于测试
   */
  async deleteSubscriptionForTestRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<void>> {
    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    const urlPath = `/api/v1/subscription/deleteSubscriptionForTest`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
      },
      initOverrides,
    );

    return new runtime.VoidApiResponse(response);
  }

  /**
   * 删除订阅，用于测试
   * 删除订阅，用于测试
   */
  async deleteSubscriptionForTest(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<void> {
    await this.deleteSubscriptionForTestRaw(initOverrides);
  }

  /**
   * Query and return current user subscription information
   * Find current user subscription
   */
  async findSubscriptionRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<SubscriptionDto>> {
    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    const urlPath = `/api/v1/subscription/findSubscription`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) => SubscriptionDtoFromJSON(jsonValue));
  }

  /**
   * Query and return current user subscription information
   * Find current user subscription
   */
  async findSubscription(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<SubscriptionDto> {
    const response = await this.findSubscriptionRaw(initOverrides);
    return await response.value();
  }

  /**
   * 处理来自 Apple App Store 的服务器通知事件（生产环境）
   * 处理 Apple App Store Server Notifications
   */
  async handleAppleNotificationRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<void>> {
    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    const urlPath = `/webhook/v1/apple`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
      },
      initOverrides,
    );

    return new runtime.VoidApiResponse(response);
  }

  /**
   * 处理来自 Apple App Store 的服务器通知事件（生产环境）
   * 处理 Apple App Store Server Notifications
   */
  async handleAppleNotification(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<void> {
    await this.handleAppleNotificationRaw(initOverrides);
  }

  /**
   * 处理来自 Apple App Store 的服务器通知事件（沙盒环境）
   * 处理 Apple App Store Server Notifications (Sandbox)
   */
  async handleAppleSandboxNotificationRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<void>> {
    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    const urlPath = `/webhook/v1/apple-sandbox`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
      },
      initOverrides,
    );

    return new runtime.VoidApiResponse(response);
  }

  /**
   * 处理来自 Apple App Store 的服务器通知事件（沙盒环境）
   * 处理 Apple App Store Server Notifications (Sandbox)
   */
  async handleAppleSandboxNotification(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<void> {
    await this.handleAppleSandboxNotificationRaw(initOverrides);
  }

  /**
   * 接收并处理 Stripe 的 webhook 事件
   * 处理 Stripe Webhook
   */
  async handleStripeWebhookRaw(
    requestParameters: SubscriptionControllerHandleStripeWebhookRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<void>> {
    if (requestParameters.stripeSignature == null) {
      throw new runtime.RequiredError(
        'stripeSignature',
        'Required parameter "stripeSignature" was null or undefined when calling handleStripeWebhook().',
      );
    }

    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    if (requestParameters.stripeSignature != null) {
      headerParameters['stripe-signature'] = String(requestParameters.stripeSignature);
    }

    const urlPath = `/webhook/v1/stripe`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
      },
      initOverrides,
    );

    return new runtime.VoidApiResponse(response);
  }

  /**
   * 接收并处理 Stripe 的 webhook 事件
   * 处理 Stripe Webhook
   */
  async handleStripeWebhook(
    stripeSignature: string,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<void> {
    await this.handleStripeWebhookRaw({ stripeSignature: stripeSignature }, initOverrides);
  }

  /**
   * 用户主动变更订阅的产品等级和计费周期，支持升级、降级和周期变更
   * 变更订阅配置
   */
  async updateSubscriptionRaw(
    requestParameters: SubscriptionControllerUpdateSubscriptionRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<void>> {
    if (requestParameters.updateSubscriptionDto == null) {
      throw new runtime.RequiredError(
        'updateSubscriptionDto',
        'Required parameter "updateSubscriptionDto" was null or undefined when calling updateSubscription().',
      );
    }

    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    headerParameters['Content-Type'] = 'application/json';

    const urlPath = `/api/v1/subscription/updateSubscription`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
        body: UpdateSubscriptionDtoToJSON(requestParameters.updateSubscriptionDto),
      },
      initOverrides,
    );

    return new runtime.VoidApiResponse(response);
  }

  /**
   * 用户主动变更订阅的产品等级和计费周期，支持升级、降级和周期变更
   * 变更订阅配置
   */
  async updateSubscription(
    updateSubscriptionDto: UpdateSubscriptionDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<void> {
    await this.updateSubscriptionRaw(
      { updateSubscriptionDto: updateSubscriptionDto },
      initOverrides,
    );
  }

  /**
   * 验证 Apple 内购交易的有效性，确认交易属于当前用户，并更新用户的订阅状态
   * 验证 Apple 内购交易
   */
  async verifyTransactionRaw(
    requestParameters: SubscriptionControllerVerifyTransactionRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<UserWithPreferenceSpaceDto>> {
    if (requestParameters.verifyTransactionDto == null) {
      throw new runtime.RequiredError(
        'verifyTransactionDto',
        'Required parameter "verifyTransactionDto" was null or undefined when calling verifyTransaction().',
      );
    }

    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    headerParameters['Content-Type'] = 'application/json';

    const urlPath = `/api/v1/subscription/verifyTransaction`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
        body: VerifyTransactionDtoToJSON(requestParameters.verifyTransactionDto),
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) =>
      UserWithPreferenceSpaceDtoFromJSON(jsonValue),
    );
  }

  /**
   * 验证 Apple 内购交易的有效性，确认交易属于当前用户，并更新用户的订阅状态
   * 验证 Apple 内购交易
   */
  async verifyTransaction(
    verifyTransactionDto: VerifyTransactionDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<UserWithPreferenceSpaceDto> {
    const response = await this.verifyTransactionRaw(
      { verifyTransactionDto: verifyTransactionDto },
      initOverrides,
    );
    return await response.value();
  }
}
