import './index.css';

import { SVG as SVGBase, SVGOptions } from '@repo/editor-common';
import { ReactNodeViewRenderer } from '@tiptap/react';
import { ISVGEditorController, SVGComponent } from './svg-component';

export type { ISVGEditorController };

export interface SVGBusinessOptions extends SVGOptions {
  svgController: ISVGEditorController;
}

export const SVG = SVGBase.extend<SVGBusinessOptions>({
  addNodeView() {
    return ReactNodeViewRenderer(SVGComponent);
  },
});
