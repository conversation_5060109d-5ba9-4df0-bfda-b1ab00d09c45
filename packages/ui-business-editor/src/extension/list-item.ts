import './list-item.css';
import { ListItem as ListItemBase } from '@repo/editor-common';
import { BulletList } from './bullet-list';
import { OrderedList } from './ordered-list';
import { mergeAttributes } from '@tiptap/core';

export const ListItem = ListItemBase.extend({
  addOptions() {
    return {
      HTMLAttributes: {
        class: 'youmind-editor-node-list-item-ui',
      },
      bulletListTypeName: BulletList.name,
      orderedListTypeName: OrderedList.name,
    };
  },

  renderHTML({ HTMLAttributes, node }) {
    return [
      'li',
      mergeAttributes(this.options.HTMLAttributes, HTMLAttributes),
      0,
    ];
  },
});
