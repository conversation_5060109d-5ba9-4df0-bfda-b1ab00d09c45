import './list-item.css';
import { ListItem as ListItemBase } from '@repo/editor-common';
import { BulletList } from './bullet-list';
import { OrderedList } from './ordered-list';
import { mergeAttributes } from '@tiptap/core';

export const ListItem = ListItemBase.extend({
  addOptions() {
    return {
      HTMLAttributes: {
        class: 'youmind-editor-node-list-item-ui',
      },
      bulletListTypeName: BulletList.name,
      orderedListTypeName: OrderedList.name,
    };
  },

  renderHTML({ HTMLAttributes, node }) {
    const contextClass = parentType === BulletList.name ? 'youmind-editor-list-item-in-bullet' : 'youmind-editor-list-item-in-ordered';
    return [
      'li',
      mergeAttributes(this.options.HTMLAttributes, HTMLAttributes, {
        class: `${this.options.HTMLAttributes.class} ${contextClass}`.trim(),
      }),
      0,
    ];
  },
});
